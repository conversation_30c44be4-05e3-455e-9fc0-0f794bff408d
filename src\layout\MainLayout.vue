<template>
  <a-layout class="main-layout" has-sider>
    <!-- 移动端遮罩层 -->
    <div
      v-if="isMobile && mobileMenuVisible"
      class="mobile-overlay"
      @click="mobileMenuVisible = false"
    ></div>

    <!-- 侧边栏 -->
    <a-layout-sider
      v-model:collapsed="collapsed"
      :trigger="null"
      collapsible
      :width="220"
      :collapsed-width="80"
      :class="['layout-sider', { 'mobile-visible': mobileMenuVisible }]"
      breakpoint="lg"
      @breakpoint="onBreakpoint"
    >
      <!-- Logo区域 -->
      <div class="logo-container">
        <div class="logo">
          <DingtalkOutlined class="logo-icon" />
          <transition name="fade">
            <span v-if="!collapsed" class="logo-text">Combo</span>
          </transition>
        </div>
      </div>

      <!-- 导航菜单 -->
      <a-menu
        v-model:selectedKeys="selectedKeys"
        v-model:openKeys="openKeys"
        theme="light"
        mode="inline"
        class="side-menu"
      >
        <a-menu-item key="dashboard">
          <DashboardOutlined />
          <span>工作台</span>
        </a-menu-item>

        <a-sub-menu key="financial">
          <template #icon><WalletOutlined /></template>
          <template #title>财务管理</template>
          <a-menu-item key="transactions">交易记录</a-menu-item>
          <a-menu-item key="reports">财务报表</a-menu-item>
        </a-sub-menu>

        <a-menu-item key="analytics">
          <LineChartOutlined />
          <span>数据分析</span>
        </a-menu-item>

        <a-menu-item key="users">
          <TeamOutlined />
          <span>用户管理</span>
        </a-menu-item>

        <a-menu-item key="settings">
          <SettingOutlined />
          <span>系统设置</span>
        </a-menu-item>
      </a-menu>
    </a-layout-sider>

    <a-layout class="main-content-layout">
      <!-- 顶部导航栏 -->
      <a-layout-header class="layout-header">
        <div class="header-content">
          <!-- 左侧区域 -->
          <div class="header-left">
            <!-- 菜单伸缩按钮 -->
            <a-button type="text" class="trigger-btn" @click="toggleCollapsed">
              <MenuUnfoldOutlined v-if="collapsed" />
              <MenuFoldOutlined v-else />
            </a-button>

            <!-- 面包屑导航 -->
            <a-breadcrumb class="breadcrumb">
              <a-breadcrumb-item>
                <HomeOutlined />
              </a-breadcrumb-item>
              <a-breadcrumb-item>工作台</a-breadcrumb-item>
            </a-breadcrumb>
          </div>

          <!-- 右侧区域 -->
          <div class="header-right">
            <!-- 搜索框 -->
            <div class="search-box">
              <a-input-search
                v-model:value="searchText"
                placeholder="搜索功能、页面、数据..."
                allow-clear
                enter-button
                @focus="searchExpanded = true"
                @blur="searchExpanded = false"
                @search="handleSearch"
              >
                <template #enterButton>
                  <a-button type="primary" class="search-button">
                    <SearchOutlined />
                  </a-button>
                </template>
              </a-input-search>
            </div>

            <!-- 通知按钮 -->
            <a-badge :count="5" class="notification-badge">
              <a-button type="text" class="header-btn">
                <BellOutlined />
              </a-button>
            </a-badge>

            <!-- 主题切换 -->
            <a-button type="text" class="header-btn" @click="toggleTheme">
              <BulbOutlined v-if="!isDark" />
              <BulbFilled v-else />
            </a-button>

            <!-- 用户信息 -->
            <a-dropdown>
              <div class="user-info">
                <a-avatar class="user-avatar" :size="32">
                  <template #icon><UserOutlined /></template>
                </a-avatar>
                <span class="user-name">Admin</span>
                <DownOutlined class="dropdown-icon" />
              </div>
              <template #overlay>
                <a-menu>
                  <a-menu-item key="profile">
                    <UserOutlined /> 个人中心
                  </a-menu-item>
                  <a-menu-item key="settings">
                    <SettingOutlined /> 账号设置
                  </a-menu-item>
                  <a-menu-divider />
                  <a-menu-item key="logout">
                    <LogoutOutlined /> 退出登录
                  </a-menu-item>
                </a-menu>
              </template>
            </a-dropdown>
          </div>
        </div>
      </a-layout-header>

      <!-- 主内容区域 -->
      <a-layout-content class="layout-content">
        <div class="content-wrapper">
          <router-view v-slot="{ Component }">
            <transition name="fade-slide" mode="out-in">
              <component :is="Component" />
            </transition>
          </router-view>
        </div>
      </a-layout-content>

      <!-- 底部信息 -->
      <a-layout-footer class="layout-footer">
        <div class="footer-content">
          <span>© 2024 iBanko Dashboard. All rights reserved.</span>
          <span class="footer-version">v1.0.0</span>
        </div>
      </a-layout-footer>
    </a-layout>
  </a-layout>
</template>

<script setup lang="ts">
import { ref, computed, watch } from "vue";
import { useRouter } from "vue-router";
import {
  MenuFoldOutlined,
  MenuUnfoldOutlined,
  DashboardOutlined,
  WalletOutlined,
  LineChartOutlined,
  TeamOutlined,
  SettingOutlined,
  BellOutlined,
  UserOutlined,
  LogoutOutlined,
  HomeOutlined,
  DownOutlined,
  BankOutlined,
  BulbOutlined,
  BulbFilled,
  SearchOutlined,
  DingtalkOutlined,
} from "@ant-design/icons-vue";

// 响应式状态
const collapsed = ref(false);
const selectedKeys = ref(["dashboard"]);
const openKeys = ref([""]);
const searchText = ref("");
const searchExpanded = ref(false);
const isDark = ref(false);
const isMobile = ref(false);
const mobileMenuVisible = ref(false);

// 路由实例
const router = useRouter();

// 切换折叠状态
const toggleCollapsed = () => {
  if (isMobile.value) {
    mobileMenuVisible.value = !mobileMenuVisible.value;
  } else {
    collapsed.value = !collapsed.value;
  }
};

// 切换主题
const toggleTheme = () => {
  isDark.value = !isDark.value;
  document.documentElement.setAttribute(
    "data-theme",
    isDark.value ? "dark" : "light"
  );
};

// 处理搜索
const handleSearch = (value: string) => {
  console.log("搜索内容:", value);
  // 这里可以添加搜索逻辑
};

// 响应式断点处理
const onBreakpoint = (broken: boolean) => {
  isMobile.value = broken;
  if (broken) {
    collapsed.value = true;
    mobileMenuVisible.value = false;
  }
};

// 监听路由变化
watch(
  () => router.currentRoute.value.path,
  (path) => {
    // 根据路径更新选中的菜单项
    // 这里可以根据实际路由配置进行映射
  }
);
</script>

<style lang="less" scoped>
// 全局样式重置
:global(html, body) {
  overflow-x: hidden;
  margin: 0;
  padding: 0;
}

.main-layout {
  min-height: 100vh;
  font-family: Inter, -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto,
    sans-serif;
  overflow-x: hidden;
  position: relative;
  width: 100%;
  max-width: 100vw;
}

// 移动端遮罩层
.mobile-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: var(--theme-fill-quaternary);
  z-index: 9;
  display: none;

  @media (max-width: 992px) {
    display: block;
  }
}

// 主内容布局
.main-content-layout {
  margin-left: 220px;
  transition: margin-left 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
  min-height: 100vh;

  @media (max-width: 992px) {
    margin-left: 0;
  }
}

// 侧边栏折叠时的布局调整
.main-layout :deep(.ant-layout-sider-collapsed) + .main-content-layout {
  margin-left: 80px;

  @media (max-width: 992px) {
    margin-left: 0;
  }
}

// 侧边栏样式
.layout-sider {
  background: var(--theme-bg-container);
  box-shadow: 4px 0 20px var(--theme-fill-quaternary);
  position: fixed;
  left: 0;
  top: 0;
  bottom: 0;
  z-index: 10;
  border-radius: 0 16px 16px 0;
  transition: all 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
  overflow: hidden;

  &.ant-layout-sider-collapsed {
    border-radius: 0 12px 12px 0;

    .logo-container {
      padding: 16px 8px;

      .logo {
        justify-content: center;

        .logo-icon {
          margin: 0;
        }
      }
    }
  }

  @media (max-width: 992px) {
    transform: translateX(-100%);

    &.mobile-visible {
      transform: translateX(0);
    }
  }
}

// Logo区域
.logo-container {
  height: 64px;
  padding: 16px 24px;
  margin: 12px;
  border-radius: 12px;
  background: linear-gradient(135deg, var(--theme-primary-bg) 0%, var(--theme-primary-border) 50%, var(--theme-bg-elevated) 100%);
  transition: all 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
  position: relative;
  overflow: hidden;

  // 添加微妙的光泽效果
  &::before {
    content: "";
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.1), transparent);
    transition: left 0.6s ease;
  }

  &:hover::before {
    left: 100%;
  }

  .logo {
    display: flex;
    align-items: center;
    height: 100%;
    position: relative;
    z-index: 1;

    .logo-icon {
      font-size: 28px;
      color: var(--theme-primary);
      transition: all 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
      filter: drop-shadow(0 2px 8px var(--theme-primary-bg));
    }

    .logo-text {
      margin-left: 12px;
      font-size: 18px;
      font-weight: 600;
      background: linear-gradient(135deg, var(--theme-primary) 0%, var(--theme-info) 100%);
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
      background-clip: text;
      white-space: nowrap;
      letter-spacing: 0.5px;
      transition: all 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
    }
  }

  &:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px var(--theme-primary-bg);

    .logo-icon {
      transform: scale(1.05);
      filter: drop-shadow(0 4px 12px var(--theme-primary-bg));
    }
  }
}

// 侧边菜单
.side-menu {
  border-right: none;
  padding: 8px 12px;

  :deep(.ant-menu-item),
  :deep(.ant-menu-submenu-title) {
    margin: 6px 0;
    border-radius: 12px;
    transition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
    position: relative;
    overflow: hidden;

    &:hover {
      background: var(--theme-bg-elevated);
      transform: translateX(4px);
      box-shadow: 0 4px 12px var(--theme-primary-bg);
    }

    &.ant-menu-item-selected {
      background: var(--theme-primary-bg);
      color: var(--theme-primary);
      font-weight: 500;
      box-shadow: 0 4px 16px var(--theme-primary-bg);

      &::after {
        display: none;
      }

      &::before {
        content: "";
        position: absolute;
        left: 0;
        top: 50%;
        transform: translateY(-50%);
        width: 4px;
        height: 60%;
        background: var(--theme-primary);
        border-radius: 0 4px 4px 0;
      }
    }
  }

  :deep(.ant-menu-submenu) {
    .ant-menu-submenu-arrow {
      transition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
    }
  }
}

// 顶部导航栏
.layout-header {
  background: var(--theme-bg-container);
  padding: 0 24px;
  margin: 0;
  border-radius: 0;
  box-shadow: 0 4px 20px var(--theme-fill-quaternary);
  position: sticky;
  top: 0;
  z-index: 9;
  backdrop-filter: blur(10px);
  background: var(--theme-bg-container);
  width: 100%;

  .header-content {
    display: flex;
    align-items: center;
    justify-content: space-between;
    height: 100%;
  }

  .header-left {
    display: flex;
    align-items: center;
    gap: 16px;

    .trigger-btn {
      font-size: 18px;
      width: 40px;
      height: 40px;
      border-radius: 12px;
      transition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);

      &:hover {
        background: var(--theme-bg-elevated);
        box-shadow: 0 4px 12px var(--theme-primary-bg);
        transform: translateY(-2px);
      }
    }

    .breadcrumb {
      @media (max-width: 576px) {
        display: none;
      }
    }
  }

  .header-right {
    display: flex;
    align-items: center;
    gap: 8px;

    .search-box {
      display: flex;
      align-items: center;

      @media (max-width: 576px) {
        display: none;
      }
    }

    .header-search {
      transition: all 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
      
      :deep(.ant-input-search) {
        border-radius: 10px;
        overflow: hidden;
        box-shadow: 0 2px 8px var(--theme-fill-quaternary);
        transition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
        border: 1px solid transparent;
        height: 36px;

        &:hover {
          box-shadow: 0 4px 12px var(--theme-primary-bg);
          border-color: var(--theme-primary-border);
        }

        &:focus-within {
          box-shadow: 0 0 0 3px var(--theme-primary-bg),
            0 4px 12px var(--theme-primary-bg);
          border-color: var(--theme-primary);
        }
      }

      :deep(.ant-input) {
        border: none;
        border-radius: 10px 0 0 10px;
        transition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
        background: var(--theme-bg-elevated);
        height: 32px;
        padding: 6px 12px;
        font-size: 13px;
        line-height: 1.4;

        &:focus {
          box-shadow: none;
          background: var(--theme-bg-container);
        }

        &::placeholder {
          color: var(--theme-text-tertiary);
          font-size: 13px;
        }
      }

      :deep(.ant-input-search-button) {
        border-radius: 0 10px 10px 0;
        border: none;
        height: 36px;
        min-width: 50px;
        background: var(--theme-primary);
        transition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
        display: flex;
        align-items: center;
        justify-content: center;

        &:hover {
          background: var(--theme-primary-hover);
          box-shadow: 0 2px 8px var(--theme-primary-bg);
          transform: translateY(-1px);
        }

        &:active {
          transform: translateY(0);
        }

        .anticon {
          font-size: 14px;
          color: #ffffff;
        }
      }

      // 自定义搜索按钮样式
      :deep(.search-button) {
        border: none;
        box-shadow: none;
        height: 36px;
        padding: 0 12px;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 14px;
        border-radius: 0 10px 10px 0;

        .anticon {
          color: #ffffff;
        }

        &:hover {
          background: var(--theme-primary-hover) !important;
          border: none !important;
          box-shadow: none !important;
        }
      }

      // 清除按钮样式优化
      :deep(.ant-input-clear-icon) {
        color: var(--theme-text-quaternary);
        transition: all 0.3s;

        &:hover {
          color: var(--theme-text-tertiary);
        }
      }
    }

    .header-btn {
      font-size: 16px;
      padding: 4px 12px;
      height: 40px;
      border-radius: 12px;
      transition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);

      &:hover {
        background: var(--theme-bg-elevated);
        box-shadow: 0 4px 12px var(--theme-primary-bg);
        transform: translateY(-2px);
      }
    }

    .notification-badge {
      :deep(.ant-badge-count) {
        top: 8px;
        right: 8px;
      }
    }

    .user-info {
      display: flex;
      align-items: center;
      gap: 8px;
      padding: 4px 12px;
      border-radius: 12px;
      cursor: pointer;
      transition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);

      &:hover {
        background: var(--theme-bg-elevated);
        box-shadow: 0 4px 12px var(--theme-primary-bg);
        transform: translateY(-2px);
      }

      .user-name {
        font-weight: 500;
        color: var(--theme-text-base);

        @media (max-width: 768px) {
          display: none;
        }
      }

      .dropdown-icon {
        font-size: 12px;
        color: var(--theme-text-secondary);
      }
    }
  }
}

// 主内容区域
.layout-content {
  margin: 24px;

  @media (max-width: 576px) {
    margin: 16px;
  }

  .content-wrapper {
    background: var(--theme-bg-container);
    border-radius: 20px;
    padding: 32px;
    min-height: calc(100vh - 64px - 48px - 60px);
    box-shadow: 0 8px 32px var(--theme-fill-quaternary);
    transition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
    position: relative;
    overflow: hidden;
    max-width: 100%;

    &::before {
      content: "";
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      height: 4px;
      background: linear-gradient(90deg, var(--theme-primary) 0%, var(--theme-success) 100%);
      border-radius: 20px 20px 0 0;
    }

    &:hover {
      box-shadow: 0 12px 40px var(--theme-fill-quaternary);
      transform: translateY(-2px);
    }
  }
}

// 底部信息
.layout-footer {
  text-align: center;
  background: transparent;
  padding: 16px 24px;

  .footer-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
    color: var(--theme-text-secondary);
    font-size: 14px;

    @media (max-width: 576px) {
      flex-direction: column;
      gap: 8px;
    }
  }
}

// 动画效果
.fade-enter-active,
.fade-leave-active {
  transition: opacity 0.3s;
}

.fade-enter-from,
.fade-leave-to {
  opacity: 0;
}

.fade-slide-enter-active,
.fade-slide-leave-active {
  transition: all 0.3s cubic-bezier(0.645, 0.045, 0.355, 1);
}

.fade-slide-enter-from {
  opacity: 0;
  transform: translateX(-20px);
}

.fade-slide-leave-to {
  opacity: 0;
  transform: translateX(20px);
}

// 主题系统会自动处理暗色主题，无需手动定义




</style>
